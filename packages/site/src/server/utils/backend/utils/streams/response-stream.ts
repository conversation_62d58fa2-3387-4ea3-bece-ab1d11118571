import { createParser, type EventSourceParser, type ParsedEvent, type ReconnectInterval } from 'eventsource-parser'

/**
 * Custom parser for ResponseStream data.
 */
export interface ResponseStreamParser {
  (data: string): string | undefined
}

/**
 * Creates a TransformStream that parses events from an EventSource stream using a custom parser.
 */
export function createEventStreamTransformer(parser?: ResponseStreamParser): TransformStream<Uint8Array, Uint8Array> {
  const textDecoder = new TextDecoder()
  const textEncoder = new TextEncoder()
  let eventSourceParser: EventSourceParser

  return new TransformStream({
    start(controller) {
      eventSourceParser = createParser((event: ParsedEvent | ReconnectInterval) => {
        if (
          ('data' in event
            && event.type === 'event'
            && event.data === '[DONE]')
          // Replicate doesn't send [DONE] but does send a 'done' event
          // @see https://replicate.com/docs/streaming
          || (event as any).event === 'done'
        ) {
          controller.terminate()
          return
        }
        if ('data' in event) {
          const message = parser ? parser(event.data) : event.data
          if (message) {
            controller.enqueue(textEncoder.encode(message))
          }
        }
      })
    },

    transform(chunk) {
      eventSourceParser.feed(textDecoder.decode(chunk))
    },
  })
}

/**
 * Creates an empty ReadableStream that immediately closes upon creation.
 * This function is used as a fallback for creating a ReadableStream when the response body is null or undefined,
 * ensuring that the subsequent pipeline processing doesn't fail due to a lack of a stream.
 *
 * @returns {ReadableStream} An empty and closed ReadableStream instance.
 */
function createEmptyReadableStream(): ReadableStream {
  return new ReadableStream({
    start(controller) {
      controller.close()
    },
  })
}

/**
 * Returns a ReadableStream created from the response, parsed and handled with custom logic.
 * The stream goes through two transformation stages, first parsing the events and then
 * invoking the provided callbacks.
 *
 * For 2xx HTTP responses:
 * - The function continues with standard stream processing.
 *
 * For non-2xx HTTP responses:
 * - If the response body is defined, it asynchronously extracts and decodes the response body.
 * - It then creates a custom ReadableStream to propagate a detailed error message.
 */
export function responseStream(response: ReadableStream | null, parser?: ResponseStreamParser): ReadableStream<Uint8Array> {
  // console.log('response.ok', response.ok)
  // console.log('response.body', response.body)
  // if (!response.ok) {
  //   if (response.body) {
  //     const reader = response.body.getReader()
  //     return new ReadableStream({
  //       async start(controller) {
  //         const { done, value } = await reader.read()
  //         if (!done) {
  //           const errorText = new TextDecoder().decode(value)
  //           controller.error(new Error(`Response error: ${errorText}`))
  //         }
  //       }
  //     })
  //   } else {
  //     return new ReadableStream({
  //       start(controller) {
  //         controller.error(new Error('Response error: No response body'))
  //       }
  //     })
  //   }
  // }

  const responseBodyStream = response || createEmptyReadableStream()
  return responseBodyStream.pipeThrough(createEventStreamTransformer(parser))
}
